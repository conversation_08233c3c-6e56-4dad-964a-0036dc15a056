#!/usr/bin/env python3
"""
Test script to verify the behavior of different modes and file locations
"""

import os
import sys
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.abspath('.'))

from config.settings import SettingsManager
from config.data_operations import create_default_settings

def test_mode_behavior(mode_name):
    """Test the behavior of a specific mode"""
    print(f"\n{'='*50}")
    print(f"Testing mode: {mode_name}")
    print(f"{'='*50}")
    
    try:
        # Create SettingsManager with specific mode
        settings_manager = SettingsManager(mode=mode_name)
        
        print(f"Mode detected: {settings_manager.mode}")
        print(f"Base directory: {settings_manager.base_dir}")
        print(f"Config directory: {settings_manager.config_dir}")
        print(f"Data file path: {settings_manager.data_file}")
        
        # Check if data file exists
        if settings_manager.data_file.exists():
            print(f"✅ Data file exists: {settings_manager.data_file}")
            
            # Load and check content
            with open(settings_manager.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                run_mode = data.get('system', {}).get('run_mode', 'NOT_FOUND')
                print(f"   run_mode in file: {run_mode}")
        else:
            print(f"❌ Data file does not exist: {settings_manager.data_file}")
        
        # Test log file path
        log_path = settings_manager._get_log_file_path()
        print(f"Log file path: {log_path}")
        
        # Check if directories exist
        data_dir = settings_manager.data_file.parent
        print(f"Data directory exists: {data_dir.exists()} ({data_dir})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing mode {mode_name}: {e}")
        return False

def check_current_file_structure():
    """Check what files currently exist"""
    print(f"\n{'='*50}")
    print("Current file structure")
    print(f"{'='*50}")
    
    base_dir = Path(".")
    
    # Check for data files
    data_files = [
        "data.json",
        "data_dev.json", 
        "dist/dev/data_dev.json",
        "dist/production/data.json"
    ]
    
    for file_path in data_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"✅ Found: {file_path}")
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    run_mode = data.get('system', {}).get('run_mode', 'NOT_FOUND')
                    print(f"   run_mode: {run_mode}")
            except Exception as e:
                print(f"   Error reading: {e}")
        else:
            print(f"❌ Missing: {file_path}")
    
    # Check for log files
    log_files = [
        "build_dev_debug.log",
        "dist/dev/dev_debug.log",
        "dist/dev/build_dev_debug.log"
    ]
    
    print(f"\nLog files:")
    for file_path in log_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")

def create_test_files():
    """Create test files to simulate different modes"""
    print(f"\n{'='*50}")
    print("Creating test files")
    print(f"{'='*50}")
    
    # Create directories
    os.makedirs("dist/dev", exist_ok=True)
    os.makedirs("dist/production", exist_ok=True)
    
    # Create test data files
    test_files = [
        ("data_dev.json", "build-dev"),
        ("dist/dev/data_dev.json", "build-dev"),
        ("dist/production/data.json", "build-final")
    ]
    
    for file_path, run_mode in test_files:
        settings = create_default_settings()
        settings.system["run_mode"] = run_mode
        
        # Create a temporary SettingsManager to serialize
        temp_manager = SettingsManager(mode=run_mode)
        temp_manager.settings = settings
        settings_dict = temp_manager._serialize_settings()
        
        full_path = Path(file_path)
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            json.dump(settings_dict, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Created: {file_path} with run_mode: {run_mode}")

def main():
    """Main test function"""
    print("Writing Tools - Mode Testing Script")
    print("This script tests the behavior of different modes")
    
    # Check current structure
    check_current_file_structure()
    
    # Create test files
    create_test_files()
    
    # Test each mode
    modes_to_test = ["dev", "build-dev", "build-final"]
    
    for mode in modes_to_test:
        test_mode_behavior(mode)
    
    print(f"\n{'='*50}")
    print("Testing completed!")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
